{% extends "base.html" %}

{% block title %}Employer Dashboard - JobSync{% endblock %}

{% block extra_css %}
<style>
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.firebase-status {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
}
</style>
{% endblock %}

{% block content %}
<!-- Firebase Status Indicator -->
<div id="firebaseStatus" class="firebase-status">
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-cloud me-2"></i>
        <span id="statusText">Connecting to Firebase...</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Welcome back, {{ session.username }}!
            <span class="user-type-badge user-type-employer ms-2">Employer</span>
            <span class="badge bg-success ms-2">
                <i class="fas fa-cloud me-1"></i>Firebase Enabled
            </span>
        </h1>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4" id="statsCards">
    <div class="col-md-4 mb-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-briefcase fa-2x text-primary mb-2"></i>
                <h3 class="card-title" id="jobsCount">
                    <div class="loading-spinner"></div>
                </h3>
                <p class="card-text text-muted">Active Jobs</p>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-success mb-2"></i>
                <h3 class="card-title" id="applicationsCount">
                    <div class="loading-spinner"></div>
                </h3>
                <p class="card-text text-muted">Total Applications</p>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-eye fa-2x text-warning mb-2"></i>
                <h3 class="card-title" id="pendingCount">
                    <div class="loading-spinner"></div>
                </h3>
                <p class="card-text text-muted">Pending Reviews</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('firebase_post_job') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus fa-2x mb-2"></i>
                            <br>Post New Job
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('newsfeed') }}" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-newspaper fa-2x mb-2"></i>
                            <br>News Feed
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('chat') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <br>SuperChat
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('jobs') }}" class="btn btn-outline-secondary btn-lg w-100">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <br>Browse Jobs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Management -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>Your Job Postings
                    <span class="badge bg-info ms-2" id="jobsBadge">Firebase</span>
                </h5>
                <a href="{{ url_for('firebase_post_job') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Post New Job
                </a>
            </div>
            <div class="card-body" id="jobsContainer">
                <div class="text-center py-5">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Loading jobs from Firebase...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Applications -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Recent Applications
                    <span class="badge bg-info ms-2">Firebase</span>
                </h5>
            </div>
            <div class="card-body" id="applicationsContainer">
                <div class="text-center py-5">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Loading applications from Firebase...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Firebase Configuration -->
<script type="module" src="{{ url_for('static', filename='js/firebase-config.js') }}"></script>
<script type="module" src="{{ url_for('static', filename='js/employer-firebase.js') }}"></script>

<script>
let employerService;
let currentEmployerId = '{{ session.user_id }}'; // Using session user_id as employer ID

// Wait for Firebase to initialize
setTimeout(async function() {
    try {
        employerService = window.employerFirebaseService;

        if (employerService) {
            updateFirebaseStatus('Connected to Firebase', 'success');
            await loadDashboardData();
        } else {
            throw new Error('Firebase service not available');
        }
    } catch (error) {
        console.error('Firebase initialization error:', error);
        updateFirebaseStatus('Firebase connection failed', 'danger');
    }
}, 2000);

function updateFirebaseStatus(message, type) {
    const statusDiv = document.getElementById('firebaseStatus');
    const statusText = document.getElementById('statusText');
    const alertDiv = statusDiv.querySelector('.alert');

    statusText.textContent = message;
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;

    if (type === 'success') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
}

async function loadDashboardData() {
    try {
        // Load jobs
        const jobsResult = await employerService.getJobsByEmployer(currentEmployerId);
        if (jobsResult.success) {
            displayJobs(jobsResult.data);
            document.getElementById('jobsCount').textContent = jobsResult.data.length;
        }

        // Load applications
        const appsResult = await employerService.getApplicationsByEmployer(currentEmployerId);
        if (appsResult.success) {
            displayApplications(appsResult.data);
            document.getElementById('applicationsCount').textContent = appsResult.data.length;

            const pendingApps = appsResult.data.filter(app => app.status === 'pending');
            document.getElementById('pendingCount').textContent = pendingApps.length;
        }

    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showError('Failed to load dashboard data');
    }
}

function displayJobs(jobs) {
    const container = document.getElementById('jobsContainer');

    if (jobs.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No jobs posted yet</h5>
                <p class="text-muted">Create your first job posting to start receiving applications!</p>
                <a href="{{ url_for('firebase_post_job') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Post Your First Job
                </a>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Job Title</th>
                        <th>Location</th>
                        <th>Type</th>
                        <th>Applications</th>
                        <th>Posted Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    jobs.forEach(job => {
        const postedDate = new Date(job.postedAt).toLocaleDateString();
        html += `
            <tr>
                <td>
                    <strong>${job.title}</strong>
                    <br>
                    <small class="text-muted">${job.description.substring(0, 50)}...</small>
                </td>
                <td>
                    <i class="fas fa-map-marker-alt me-1"></i>
                    ${job.location}
                </td>
                <td>
                    <span class="badge bg-light text-dark">
                        ${job.jobType ? job.jobType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'N/A'}
                    </span>
                </td>
                <td>
                    <span class="badge bg-primary">
                        ${job.applicationsCount || 0}
                    </span>
                </td>
                <td>
                    <small class="text-muted">${postedDate}</small>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="viewJobDetails('${job.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteJob('${job.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

function displayApplications(applications) {
    const container = document.getElementById('applicationsContainer');

    if (applications.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No applications received yet</h5>
                <p class="text-muted">Post jobs to start receiving applications from qualified candidates!</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Applicant</th>
                        <th>Job Title</th>
                        <th>Compatibility</th>
                        <th>Status</th>
                        <th>Applied Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    applications.slice(0, 10).forEach(app => {
        const appliedDate = new Date(app.appliedAt).toLocaleDateString();
        const statusClass = app.status === 'pending' ? 'warning' :
                           app.status === 'accepted' ? 'success' : 'danger';
        const statusIcon = app.status === 'pending' ? 'clock' :
                          app.status === 'accepted' ? 'check' : 'times';

        html += `
            <tr>
                <td>
                    <strong>${app.applicantName || 'Unknown'}</strong>
                    <br>
                    <small class="text-muted">${app.applicantEmail || 'No email'}</small>
                </td>
                <td>${app.jobTitle || 'Unknown Job'}</td>
                <td>
                    <span class="compatibility-score">
                        <i class="fas fa-star me-1"></i>
                        ${app.compatibilityScore || 0}%
                    </span>
                </td>
                <td>
                    <span class="badge bg-${statusClass}">
                        <i class="fas fa-${statusIcon} me-1"></i>${app.status}
                    </span>
                </td>
                <td>
                    <small class="text-muted">${appliedDate}</small>
                </td>
                <td>
                    ${app.status === 'pending' ? `
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-success" onclick="updateApplicationStatus('${app.id}', 'accepted')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="updateApplicationStatus('${app.id}', 'rejected')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    ` : '<span class="text-muted">-</span>'}
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

async function updateApplicationStatus(applicationId, status) {
    if (!confirm(`Are you sure you want to ${status} this application?`)) {
        return;
    }

    try {
        const result = await employerService.updateApplicationStatus(applicationId, status);
        if (result.success) {
            showSuccess(`Application ${status} successfully!`);
            await loadDashboardData(); // Refresh data
        } else {
            showError('Failed to update application status');
        }
    } catch (error) {
        console.error('Error updating application status:', error);
        showError('Failed to update application status');
    }
}

async function deleteJob(jobId) {
    if (!confirm('Are you sure you want to delete this job posting?')) {
        return;
    }

    try {
        const result = await employerService.deleteJob(jobId);
        if (result.success) {
            showSuccess('Job deleted successfully!');
            await loadDashboardData(); // Refresh data
        } else {
            showError('Failed to delete job');
        }
    } catch (error) {
        console.error('Error deleting job:', error);
        showError('Failed to delete job');
    }
}

function viewJobDetails(jobId) {
    // Implement job details modal or redirect
    alert(`Job details for ID: ${jobId} (Feature to be implemented)`);
}

async function refreshData() {
    showInfo('Refreshing data...');
    await loadDashboardData();
    showSuccess('Data refreshed successfully!');
}

function showSuccess(message) {
    showToast(message, 'success');
}

function showError(message) {
    showToast(message, 'danger');
}

function showInfo(message) {
    showToast(message, 'info');
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 100px; right: 20px; z-index: 1050; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>
{% endblock %}
