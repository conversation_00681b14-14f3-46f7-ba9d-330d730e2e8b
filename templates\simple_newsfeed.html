{% extends "base.html" %}

{% block title %}News Feed - JobSync{% endblock %}

{% block extra_css %}
<style>
.post-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    border-radius: 12px;
}

.post-header {
    padding: 1rem 1.5rem 0.5rem;
}

.post-content {
    padding: 0 1.5rem 1rem;
}

.post-actions {
    padding: 0.5rem 1.5rem 1rem;
    border-top: 1px solid #e9ecef;
}

.action-btn {
    background: none;
    border: none;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.action-btn:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.action-btn.liked {
    color: #dc3545;
}

.create-post-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.post-time {
    color: #6c757d;
    font-size: 0.85rem;
}

.engagement-stats {
    color: #6c757d;
    font-size: 0.85rem;
    padding: 0.5rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.comment-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    display: none;
}

.comment-item {
    background: white;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.image-preview {
    position: relative;
    display: inline-block;
    margin: 0.5rem 0;
}

.image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    border: 2px solid rgba(255,255,255,0.3);
}

.remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
}

.post-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
    cursor: pointer;
    transition: transform 0.2s;
}

.post-image:hover {
    transform: scale(1.02);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="fas fa-newspaper me-2"></i>News Feed
                    <span class="badge bg-success ms-3">
                        <i class="fas fa-database me-1"></i>SQLite Powered
                    </span>
                </h1>
                <button class="btn btn-outline-primary" onclick="loadPosts()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>

            <!-- Create Post Card -->
            <div class="card create-post-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-plus-circle me-2"></i>Share Something Amazing
                    </h5>

                    <form id="createPostForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <textarea class="form-control" id="postContent" rows="3"
                                      placeholder="What's on your mind? Share your thoughts, achievements, or insights..."
                                      style="background: rgba(255,255,255,0.9); border: none;" maxlength="1000"></textarea>
                            <div class="form-text text-light mt-2">
                                <span id="charCount">0</span>/1000 characters
                            </div>
                        </div>

                        <!-- Image Upload Section -->
                        <div class="mb-3">
                            <input type="file" id="imageUpload" accept="image/*" style="display: none;">
                            <div id="imagePreview" class="mb-2"></div>
                            <button type="button" class="btn btn-light btn-sm" onclick="document.getElementById('imageUpload').click()">
                                <i class="fas fa-image me-1"></i>Add Photo
                            </button>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-light">
                                <i class="fas fa-globe me-1"></i>
                                Visible to all JobSync users
                            </small>
                            <button type="submit" class="btn btn-light" id="postBtn">
                                <i class="fas fa-paper-plane me-1"></i>Post
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Posts Feed -->
            <div id="postsContainer">
                <div class="text-center py-5">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Loading posts...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Comments Modal -->
<div class="modal fade" id="commentsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-comments me-2"></i>Comments
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="commentsContainer">
                    <div class="text-center py-3">
                        <div class="loading-spinner"></div>
                        <p class="mt-2 text-muted">Loading comments...</p>
                    </div>
                </div>

                <!-- Add Comment -->
                <div class="mt-3 pt-3 border-top">
                    <form id="addCommentForm">
                        <div class="input-group">
                            <input type="text" class="form-control" id="commentText"
                                   placeholder="Write a comment..." maxlength="500">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-image me-2"></i>Image View
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Full size image" style="max-width: 100%; height: auto;">
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentUser = {
    id: '{{ session.user_id }}',
    username: '{{ session.username }}',
    userType: '{{ session.user_type }}'
};
let currentCommentsPostId = null;
let selectedImage = null;

$(document).ready(function() {
    // Load posts on page load
    loadPosts();

    // Character counter
    $('#postContent').on('input', function() {
        const length = $(this).val().length;
        $('#charCount').text(length);

        if (length > 900) {
            $('#charCount').addClass('text-warning');
        } else {
            $('#charCount').removeClass('text-warning');
        }
    });

    // Image upload handling
    $('#imageUpload').change(function(e) {
        handleImageUpload(e.target.files[0]);
    });

    // Create post form submission
    $('#createPostForm').submit(async function(e) {
        e.preventDefault();
        await createPost();
    });

    // Add comment form
    $('#addCommentForm').submit(async function(e) {
        e.preventDefault();
        await addComment();
    });

    // Auto-refresh posts every 30 seconds
    setInterval(loadPosts, 30000);
});

function handleImageUpload(file) {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        showError('Please select an image file');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showError('Image size must be less than 5MB');
        return;
    }

    selectedImage = file;

    // Create preview
    const reader = new FileReader();
    reader.onload = function(e) {
        const preview = $('#imagePreview');
        preview.html(`
            <div class="image-preview">
                <img src="${e.target.result}" alt="Preview">
                <button type="button" class="remove-image" onclick="removeImage()">×</button>
            </div>
        `);
    };
    reader.readAsDataURL(file);
}

function removeImage() {
    selectedImage = null;
    $('#imagePreview').empty();
    $('#imageUpload').val('');
}

async function loadPosts() {
    try {
        const response = await fetch('/api/posts');
        const posts = await response.json();

        if (response.ok) {
            displayPosts(posts);
        } else {
            showError('Failed to load posts: ' + posts.error);
        }
    } catch (error) {
        console.error('Error loading posts:', error);
        showError('Failed to load posts');
    }
}

function displayPosts(posts) {
    const container = $('#postsContainer');

    if (posts.length === 0) {
        container.html(`
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No posts yet</h5>
                <p class="text-muted">Be the first to share something amazing!</p>
            </div>
        `);
        return;
    }

    let html = '';
    posts.forEach(post => {
        html += generatePostHTML(post);
    });

    container.html(html);
}

function generatePostHTML(post) {
    const timeAgo = getTimeAgo(post.created_at);
    const userInitials = post.username.split(' ').map(n => n[0]).join('').toUpperCase();

    // Generate image HTML if post has an image
    let imageHTML = '';
    if (post.image_path) {
        imageHTML = `
            <div class="post-image-container">
                <img src="/static/uploads/${post.image_path}"
                     alt="Post image"
                     class="post-image"
                     onclick="openImageModal('${post.image_path}')">
            </div>
        `;
    }

    return `
        <div class="card post-card">
            <div class="post-header">
                <div class="d-flex align-items-center">
                    <div class="user-avatar me-3">${userInitials}</div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${post.username}</h6>
                        <div class="d-flex align-items-center">
                            <span class="user-type-badge user-type-${post.user_type} me-2">${post.user_type}</span>
                            <span class="post-time">${timeAgo}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="post-content">
                ${post.content ? `<p>${post.content}</p>` : ''}
                ${imageHTML}
            </div>

            ${(post.likes_count > 0 || post.comments_count > 0) ? `
                <div class="engagement-stats">
                    ${post.likes_count > 0 ? `<i class="fas fa-heart text-danger me-1"></i>${post.likes_count} likes` : ''}
                    ${post.comments_count > 0 ? `<i class="fas fa-comment ms-3 me-1"></i>${post.comments_count} comments` : ''}
                </div>
            ` : ''}

            <div class="post-actions">
                <div class="d-flex justify-content-around">
                    <button class="action-btn ${post.user_liked ? 'liked' : ''}" onclick="toggleLike(${post.id})">
                        <i class="fas fa-heart me-1"></i>Like
                    </button>
                    <button class="action-btn" onclick="showComments(${post.id})">
                        <i class="fas fa-comment me-1"></i>Comment
                    </button>
                    <button class="action-btn" onclick="sharePost(${post.id})">
                        <i class="fas fa-share me-1"></i>Share
                    </button>
                </div>
            </div>
        </div>
    `;
}

async function createPost() {
    const content = $('#postContent').val().trim();

    if (!content && !selectedImage) {
        showError('Please enter some content or select an image for your post');
        return;
    }

    const postBtn = $('#postBtn');
    const originalText = postBtn.html();
    postBtn.html('<div class="loading-spinner me-1"></div>Posting...').prop('disabled', true);

    try {
        let response;

        if (selectedImage) {
            // Use FormData for file upload
            const formData = new FormData();
            formData.append('content', content);
            formData.append('image', selectedImage);

            response = await fetch('/api/posts', {
                method: 'POST',
                body: formData
            });
        } else {
            // Use JSON for text-only posts
            response = await fetch('/api/posts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content: content })
            });
        }

        const result = await response.json();

        if (response.ok) {
            showSuccess('Post created successfully!');
            $('#postContent').val('');
            $('#charCount').text('0');
            removeImage(); // Clear image preview
            loadPosts(); // Refresh posts
        } else {
            showError('Failed to create post: ' + result.error);
        }
    } catch (error) {
        console.error('Error creating post:', error);
        showError('Failed to create post');
    } finally {
        postBtn.html(originalText).prop('disabled', false);
    }
}

async function toggleLike(postId) {
    try {
        const response = await fetch(`/api/posts/${postId}/like`, {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
            loadPosts(); // Refresh to show updated likes
        } else {
            showError('Failed to update like');
        }
    } catch (error) {
        console.error('Error toggling like:', error);
        showError('Failed to update like');
    }
}

async function showComments(postId) {
    currentCommentsPostId = postId;
    $('#commentsModal').modal('show');

    try {
        const response = await fetch(`/api/posts/${postId}/comments`);
        const comments = await response.json();

        if (response.ok) {
            displayComments(comments);
        } else {
            showError('Failed to load comments');
        }
    } catch (error) {
        console.error('Error loading comments:', error);
        showError('Failed to load comments');
    }
}

function displayComments(comments) {
    const container = $('#commentsContainer');

    if (comments.length === 0) {
        container.html('<p class="text-muted text-center">No comments yet. Be the first to comment!</p>');
        return;
    }

    let html = '';
    comments.forEach(comment => {
        const timeAgo = getTimeAgo(comment.created_at);
        const userInitials = comment.username.split(' ').map(n => n[0]).join('').toUpperCase();

        html += `
            <div class="comment-item">
                <div class="d-flex">
                    <div class="user-avatar me-3" style="width: 32px; height: 32px; font-size: 0.8rem;">${userInitials}</div>
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <strong class="me-2">${comment.username}</strong>
                            <span class="user-type-badge user-type-${comment.user_type} me-2">${comment.user_type}</span>
                            <small class="text-muted">${timeAgo}</small>
                        </div>
                        <p class="mb-0">${comment.comment}</p>
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);
}

async function addComment() {
    const commentText = $('#commentText').val().trim();

    if (!commentText) {
        showError('Please enter a comment');
        return;
    }

    try {
        const response = await fetch(`/api/posts/${currentCommentsPostId}/comments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ comment: commentText })
        });

        const result = await response.json();

        if (response.ok) {
            $('#commentText').val('');
            showComments(currentCommentsPostId); // Refresh comments
            loadPosts(); // Refresh posts to update comment count
        } else {
            showError('Failed to add comment: ' + result.error);
        }
    } catch (error) {
        console.error('Error adding comment:', error);
        showError('Failed to add comment');
    }
}

function sharePost(postId) {
    showInfo('Share functionality coming soon!');
}

function openImageModal(imagePath) {
    const modalImage = document.getElementById('modalImage');
    modalImage.src = `/static/uploads/${imagePath}`;
    $('#imageModal').modal('show');
}

function getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + 'm ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + 'h ago';
    if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + 'd ago';

    return time.toLocaleDateString();
}

function showSuccess(message) {
    showToast(message, 'success');
}

function showError(message) {
    showToast(message, 'danger');
}

function showInfo(message) {
    showToast(message, 'info');
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 100px; right: 20px; z-index: 1050; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>
{% endblock %}
