<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Debug - JobSync</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .debug-console {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 1rem;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            font-size: 0.9rem;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-bug me-2"></i>Firebase Debug Console
                    <span class="badge bg-warning text-dark ms-2">Debug Mode</span>
                </h1>
                
                <!-- Status Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <span class="status-indicator status-info" id="firebase-status"></span>
                                    Firebase App
                                </h5>
                                <p class="card-text" id="firebase-text">Initializing...</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <span class="status-indicator status-info" id="firestore-status"></span>
                                    Firestore
                                </h5>
                                <p class="card-text" id="firestore-text">Waiting...</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <span class="status-indicator status-info" id="storage-status"></span>
                                    Storage
                                </h5>
                                <p class="card-text" id="storage-text">Waiting...</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">
                                    <span class="status-indicator status-info" id="service-status"></span>
                                    Service
                                </h5>
                                <p class="card-text" id="service-text">Waiting...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test Buttons -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Test Functions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-primary" onclick="testFirebaseConnection()">
                                        <i class="fas fa-plug me-1"></i>Test Connection
                                    </button>
                                    <button class="btn btn-success" onclick="testCreatePost()">
                                        <i class="fas fa-plus me-1"></i>Test Create Post
                                    </button>
                                    <button class="btn btn-info" onclick="testReadPosts()">
                                        <i class="fas fa-eye me-1"></i>Test Read Posts
                                    </button>
                                    <button class="btn btn-warning" onclick="clearConsole()">
                                        <i class="fas fa-trash me-1"></i>Clear Console
                                    </button>
                                    <button class="btn btn-secondary" onclick="window.location.href='/newsfeed'">
                                        <i class="fas fa-arrow-left me-1"></i>Back to News Feed
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Debug Console -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Debug Console</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="debug-console" id="debug-console">
                                    <div>Firebase Debug Console - JobSync</div>
                                    <div>Initializing Firebase services...</div>
                                    <div>---</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="module" src="{{ url_for('static', filename='js/newsfeed-debug.js') }}"></script>
    
    <script>
        let debugService = null;
        
        function log(message, type = 'info') {
            const console = document.getElementById('debug-console');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : type === 'warning' ? '#ffd43b' : '#00ff00';
            console.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }
        
        function updateStatus(component, status, text) {
            const statusEl = document.getElementById(`${component}-status`);
            const textEl = document.getElementById(`${component}-text`);
            
            statusEl.className = `status-indicator status-${status}`;
            textEl.textContent = text;
        }
        
        function clearConsole() {
            document.getElementById('debug-console').innerHTML = `
                <div>Firebase Debug Console - JobSync</div>
                <div>Console cleared</div>
                <div>---</div>
            `;
        }
        
        // Monitor Firebase initialization
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            checkCount++;
            
            if (window.firebaseDebugReady === true) {
                clearInterval(checkInterval);
                log('✅ Firebase debug service is ready!', 'success');
                updateStatus('firebase', 'success', 'Connected');
                updateStatus('firestore', 'success', 'Ready');
                updateStatus('storage', 'success', 'Ready');
                updateStatus('service', 'success', 'Available');
                debugService = window.debugNewsFeedService;
            } else if (window.firebaseDebugReady === false) {
                clearInterval(checkInterval);
                log('❌ Firebase debug service failed to initialize', 'error');
                updateStatus('firebase', 'error', 'Failed');
                updateStatus('firestore', 'error', 'Failed');
                updateStatus('storage', 'error', 'Failed');
                updateStatus('service', 'error', 'Failed');
            } else if (checkCount > 30) {
                clearInterval(checkInterval);
                log('⏰ Timeout waiting for Firebase initialization', 'warning');
                updateStatus('firebase', 'warning', 'Timeout');
            } else {
                log(`⏳ Waiting for Firebase... (${checkCount}/30)`, 'info');
            }
        }, 1000);
        
        async function testFirebaseConnection() {
            log('🔌 Testing Firebase connection...', 'info');
            
            if (!debugService) {
                log('❌ Debug service not available', 'error');
                return;
            }
            
            try {
                // Test basic Firestore operation
                const testData = {
                    test: true,
                    timestamp: new Date().toISOString(),
                    message: 'Connection test from debug console'
                };
                
                log('📝 Writing test document...', 'info');
                const result = await debugService.functions.addDoc(
                    debugService.functions.collection(debugService.db, 'connection-test'), 
                    testData
                );
                
                log(`✅ Test document created with ID: ${result.id}`, 'success');
                log('🎉 Firebase connection test successful!', 'success');
                
            } catch (error) {
                log(`❌ Connection test failed: ${error.message}`, 'error');
            }
        }
        
        async function testCreatePost() {
            log('📝 Testing post creation...', 'info');
            
            if (!debugService) {
                log('❌ Debug service not available', 'error');
                return;
            }
            
            try {
                const postData = {
                    authorId: 'debug-user',
                    authorName: 'Debug User',
                    authorType: 'jobseeker',
                    content: `Debug test post created at ${new Date().toLocaleString()}`,
                    type: 'original',
                    mediaUrls: []
                };
                
                log('📤 Creating test post...', 'info');
                const result = await debugService.createPost(postData);
                
                if (result.success) {
                    log(`✅ Post created successfully with ID: ${result.id}`, 'success');
                } else {
                    log(`❌ Failed to create post: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Post creation test failed: ${error.message}`, 'error');
            }
        }
        
        async function testReadPosts() {
            log('📖 Testing post reading...', 'info');
            
            if (!debugService) {
                log('❌ Debug service not available', 'error');
                return;
            }
            
            try {
                log('📥 Fetching posts...', 'info');
                const result = await debugService.getPosts(10);
                
                if (result.success) {
                    log(`✅ Retrieved ${result.data.length} posts`, 'success');
                    result.data.forEach((post, index) => {
                        log(`📄 Post ${index + 1}: "${post.content.substring(0, 50)}..." by ${post.authorName}`, 'info');
                    });
                } else {
                    log(`❌ Failed to read posts: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Post reading test failed: ${error.message}`, 'error');
            }
        }
        
        // Initial log
        log('🚀 Debug console initialized', 'info');
        log('⏳ Waiting for Firebase services...', 'info');
    </script>
</body>
</html>
