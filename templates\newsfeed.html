{% extends "base.html" %}

{% block title %}News Feed - JobSync{% endblock %}

{% block extra_css %}
<style>
.post-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    border-radius: 12px;
}

.post-header {
    padding: 1rem 1.5rem 0.5rem;
}

.post-content {
    padding: 0 1.5rem 1rem;
}

.post-actions {
    padding: 0.5rem 1.5rem 1rem;
    border-top: 1px solid #e9ecef;
}

.post-media {
    max-width: 100%;
    border-radius: 8px;
    margin: 1rem 0;
}

.post-video {
    width: 100%;
    max-height: 400px;
    border-radius: 8px;
}

.action-btn {
    background: none;
    border: none;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.action-btn:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.action-btn.liked {
    color: #dc3545;
}

.action-btn.shared {
    color: #28a745;
}

.comment-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.comment-item {
    background: white;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.create-post-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.media-preview {
    position: relative;
    display: inline-block;
    margin: 0.5rem;
}

.media-preview img, .media-preview video {
    max-width: 150px;
    max-height: 150px;
    border-radius: 8px;
}

.remove-media {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
}

.firebase-indicator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.post-time {
    color: #6c757d;
    font-size: 0.85rem;
}

.engagement-stats {
    color: #6c757d;
    font-size: 0.85rem;
    padding: 0.5rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Feed -->
        <div class="col-lg-8 mx-auto">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="fas fa-newspaper me-2"></i>News Feed
                    <span class="firebase-indicator ms-3">
                        <i class="fas fa-cloud me-1"></i>Firebase Powered
                    </span>
                </h1>
                <div>
                    <button class="btn btn-outline-secondary me-2" onclick="testFirebaseConnection()">
                        <i class="fas fa-flask me-1"></i>Test Connection
                    </button>
                    <button class="btn btn-outline-primary" onclick="refreshFeed()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Create Post Card -->
            <div class="card create-post-card">
                <div class="card-body">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-plus-circle me-2"></i>Share Something Amazing
                    </h5>

                    <form id="createPostForm">
                        <div class="mb-3">
                            <textarea class="form-control" id="postContent" rows="3"
                                      placeholder="What's on your mind? Share your thoughts, achievements, or insights..."
                                      style="background: rgba(255,255,255,0.9); border: none;"></textarea>
                        </div>

                        <!-- Media Upload -->
                        <div class="mb-3">
                            <input type="file" id="mediaUpload" multiple accept="image/*,video/*" style="display: none;">
                            <div id="mediaPreview" class="mb-2"></div>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-light btn-sm" onclick="document.getElementById('mediaUpload').click()">
                                    <i class="fas fa-image me-1"></i>Photo/Video
                                </button>
                                <button type="button" class="btn btn-light btn-sm" onclick="addPoll()">
                                    <i class="fas fa-poll me-1"></i>Poll
                                </button>
                                <button type="button" class="btn btn-light btn-sm" onclick="addEvent()">
                                    <i class="fas fa-calendar me-1"></i>Event
                                </button>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-light">
                                <i class="fas fa-globe me-1"></i>
                                Visible to all JobSync users
                            </small>
                            <button type="submit" class="btn btn-light" id="postBtn">
                                <i class="fas fa-paper-plane me-1"></i>Post
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Posts Feed -->
            <div id="postsContainer">
                <div class="text-center py-5">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Loading posts from Firebase...</p>
                </div>
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" id="loadMoreBtn" onclick="loadMorePosts()" style="display: none;">
                    <i class="fas fa-plus me-1"></i>Load More Posts
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-share me-2"></i>Share Post
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <textarea class="form-control" id="shareText" rows="3"
                          placeholder="Add your thoughts about this post..."></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmShare()">
                    <i class="fas fa-share me-1"></i>Share
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Comments Modal -->
<div class="modal fade" id="commentsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-comments me-2"></i>Comments
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="commentsContainer">
                    <div class="text-center py-3">
                        <div class="loading-spinner"></div>
                        <p class="mt-2 text-muted">Loading comments...</p>
                    </div>
                </div>

                <!-- Add Comment -->
                <div class="mt-3 pt-3 border-top">
                    <form id="addCommentForm">
                        <div class="input-group">
                            <input type="text" class="form-control" id="commentText"
                                   placeholder="Write a comment...">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Firebase Configuration -->
<script type="module" src="{{ url_for('static', filename='js/newsfeed-firebase.js') }}"></script>

<script>
let newsFeedService;
let currentUser = {
    id: '{{ session.user_id }}',
    username: '{{ session.username }}',
    userType: '{{ session.user_type }}',
    email: '{{ session.get("email", "") }}'
};
let selectedFiles = [];
let currentSharePostId = null;
let currentCommentsPostId = null;

// Wait for Firebase to initialize with better error handling
let initAttempts = 0;
const maxAttempts = 10;

function initializeNewsFeed() {
    initAttempts++;
    console.log(`Attempting to initialize news feed service (attempt ${initAttempts})`);

    newsFeedService = window.newsFeedService;
    if (newsFeedService) {
        console.log('News Feed service ready');
        showSuccess('Connected to Firebase News Feed!');
        loadPosts();
        setupRealTimeListener();
    } else if (initAttempts < maxAttempts) {
        console.log('News feed service not ready, retrying...');
        setTimeout(initializeNewsFeed, 1000);
    } else {
        console.error('Failed to initialize news feed service after', maxAttempts, 'attempts');
        showError('Failed to connect to Firebase. Please refresh the page.');

        // Show fallback message
        $('#postsContainer').html(`
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5 class="text-muted">Connection Error</h5>
                <p class="text-muted">Unable to connect to Firebase. Please check your internet connection and refresh the page.</p>
                <button class="btn btn-primary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh Page
                </button>
            </div>
        `);
    }
}

// Start initialization
setTimeout(initializeNewsFeed, 1000);

$(document).ready(function() {
    // Create post form submission
    $('#createPostForm').submit(async function(e) {
        e.preventDefault();
        await createPost();
    });

    // Media upload handling
    $('#mediaUpload').change(function(e) {
        handleMediaUpload(e.target.files);
    });

    // Add comment form
    $('#addCommentForm').submit(async function(e) {
        e.preventDefault();
        await addComment();
    });
});

async function createPost() {
    console.log('Creating post...');

    // Check if service is available
    if (!newsFeedService) {
        showError('News feed service not available. Please refresh the page.');
        return;
    }

    const content = $('#postContent').val().trim();

    if (!content && selectedFiles.length === 0) {
        showError('Please add some content or media to your post');
        return;
    }

    const postBtn = $('#postBtn');
    const originalText = postBtn.html();
    postBtn.html('<div class="loading-spinner me-1"></div>Posting...').prop('disabled', true);

    try {
        console.log('Current user:', currentUser);

        // Create post data
        const postData = {
            authorId: currentUser.id,
            authorName: currentUser.username,
            authorType: currentUser.userType,
            content: content,
            type: 'original',
            mediaUrls: []
        };

        console.log('Post data:', postData);

        // Create post first
        console.log('Calling createPost service...');
        const result = await newsFeedService.createPost(postData);
        console.log('Create post result:', result);

        if (result.success) {
            console.log('Post created successfully with ID:', result.id);

            // Upload media if any
            if (selectedFiles.length > 0) {
                console.log('Uploading', selectedFiles.length, 'media files...');
                for (let file of selectedFiles) {
                    console.log('Uploading file:', file.name);
                    const mediaResult = await newsFeedService.uploadMedia(file, result.id);
                    console.log('Media upload result:', mediaResult);

                    if (mediaResult.success) {
                        postData.mediaUrls.push({
                            url: mediaResult.url,
                            type: file.type.startsWith('image/') ? 'image' : 'video',
                            fileName: mediaResult.fileName
                        });
                    } else {
                        console.error('Failed to upload media:', mediaResult.error);
                    }
                }

                // Update post with media URLs
                if (postData.mediaUrls.length > 0) {
                    console.log('Updating post with media URLs...');
                    const updateResult = await newsFeedService.updatePost(result.id, { mediaUrls: postData.mediaUrls });
                    console.log('Update post result:', updateResult);
                }
            }

            showSuccess('Post created successfully!');

            // Clear form
            $('#postContent').val('');
            selectedFiles = [];
            $('#mediaPreview').empty();

            // Refresh feed
            console.log('Refreshing feed...');
            loadPosts();
        } else {
            console.error('Failed to create post:', result.error);
            showError('Failed to create post: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error creating post:', error);
        showError('Failed to create post: ' + error.message);
    } finally {
        postBtn.html(originalText).prop('disabled', false);
    }
}

function handleMediaUpload(files) {
    const preview = $('#mediaPreview');

    Array.from(files).forEach((file, index) => {
        if (selectedFiles.length >= 5) {
            showError('Maximum 5 files allowed');
            return;
        }

        selectedFiles.push(file);

        const mediaDiv = $('<div class="media-preview"></div>');
        const removeBtn = $('<button class="remove-media" onclick="removeMedia(' + (selectedFiles.length - 1) + ')">×</button>');

        if (file.type.startsWith('image/')) {
            const img = $('<img>');
            img.attr('src', URL.createObjectURL(file));
            mediaDiv.append(img);
        } else if (file.type.startsWith('video/')) {
            const video = $('<video controls>');
            video.attr('src', URL.createObjectURL(file));
            mediaDiv.append(video);
        }

        mediaDiv.append(removeBtn);
        preview.append(mediaDiv);
    });
}

function removeMedia(index) {
    selectedFiles.splice(index, 1);
    $('#mediaPreview').empty();

    // Re-render preview
    selectedFiles.forEach((file, i) => {
        const preview = $('#mediaPreview');
        const mediaDiv = $('<div class="media-preview"></div>');
        const removeBtn = $('<button class="remove-media" onclick="removeMedia(' + i + ')">×</button>');

        if (file.type.startsWith('image/')) {
            const img = $('<img>');
            img.attr('src', URL.createObjectURL(file));
            mediaDiv.append(img);
        } else if (file.type.startsWith('video/')) {
            const video = $('<video controls>');
            video.attr('src', URL.createObjectURL(file));
            mediaDiv.append(video);
        }

        mediaDiv.append(removeBtn);
        preview.append(mediaDiv);
    });
}

async function loadPosts() {
    console.log('Loading posts...');

    if (!newsFeedService) {
        console.error('News feed service not available');
        showError('News feed service not available');
        return;
    }

    try {
        console.log('Calling getPosts service...');
        const result = await newsFeedService.getPosts(20);
        console.log('Get posts result:', result);

        if (result.success) {
            console.log('Posts loaded successfully:', result.data.length, 'posts');
            displayPosts(result.data);
        } else {
            console.error('Failed to load posts:', result.error);
            showError('Failed to load posts: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error loading posts:', error);
        showError('Failed to load posts: ' + error.message);
    }
}

function displayPosts(posts) {
    const container = $('#postsContainer');

    if (posts.length === 0) {
        container.html(`
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No posts yet</h5>
                <p class="text-muted">Be the first to share something amazing!</p>
            </div>
        `);
        return;
    }

    let html = '';
    posts.forEach(post => {
        html += generatePostHTML(post);
    });

    container.html(html);
    $('#loadMoreBtn').show();
}

function generatePostHTML(post) {
    const timeAgo = getTimeAgo(post.createdAt);
    const isLiked = post.likes && post.likes.includes(currentUser.id);
    const userInitials = post.authorName.split(' ').map(n => n[0]).join('').toUpperCase();

    let mediaHTML = '';
    if (post.mediaUrls && post.mediaUrls.length > 0) {
        mediaHTML = '<div class="post-media-container">';
        post.mediaUrls.forEach(media => {
            if (media.type === 'image') {
                mediaHTML += `<img src="${media.url}" class="post-media" alt="Post image">`;
            } else if (media.type === 'video') {
                mediaHTML += `<video src="${media.url}" class="post-video" controls></video>`;
            }
        });
        mediaHTML += '</div>';
    }

    return `
        <div class="card post-card">
            <div class="post-header">
                <div class="d-flex align-items-center">
                    <div class="user-avatar me-3">${userInitials}</div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${post.authorName}</h6>
                        <div class="d-flex align-items-center">
                            <span class="user-type-badge user-type-${post.authorType} me-2">${post.authorType}</span>
                            <span class="post-time">${timeAgo}</span>
                        </div>
                    </div>
                    ${post.authorId === currentUser.id ? `
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="deletePost('${post.id}')">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    ` : ''}
                </div>
            </div>

            <div class="post-content">
                <p>${post.content}</p>
                ${mediaHTML}
            </div>

            ${(post.likesCount > 0 || post.commentsCount > 0 || post.sharesCount > 0) ? `
                <div class="engagement-stats">
                    ${post.likesCount > 0 ? `<i class="fas fa-heart text-danger me-1"></i>${post.likesCount} likes` : ''}
                    ${post.commentsCount > 0 ? `<i class="fas fa-comment ms-3 me-1"></i>${post.commentsCount} comments` : ''}
                    ${post.sharesCount > 0 ? `<i class="fas fa-share ms-3 me-1"></i>${post.sharesCount} shares` : ''}
                </div>
            ` : ''}

            <div class="post-actions">
                <div class="d-flex justify-content-around">
                    <button class="action-btn ${isLiked ? 'liked' : ''}" onclick="toggleLike('${post.id}')">
                        <i class="fas fa-heart me-1"></i>Like
                    </button>
                    <button class="action-btn" onclick="showComments('${post.id}')">
                        <i class="fas fa-comment me-1"></i>Comment
                    </button>
                    <button class="action-btn" onclick="sharePost('${post.id}')">
                        <i class="fas fa-share me-1"></i>Share
                    </button>
                </div>
            </div>
        </div>
    `;
}

async function toggleLike(postId) {
    try {
        const result = await newsFeedService.toggleLike(postId, currentUser.id);
        if (result.success) {
            loadPosts(); // Refresh to show updated likes
        }
    } catch (error) {
        console.error('Error toggling like:', error);
        showError('Failed to update like');
    }
}

function sharePost(postId) {
    currentSharePostId = postId;
    $('#shareModal').modal('show');
}

async function confirmShare() {
    const shareText = $('#shareText').val().trim();

    try {
        const result = await newsFeedService.sharePost(currentSharePostId, currentUser.id, shareText);
        if (result.success) {
            showSuccess('Post shared successfully!');
            $('#shareModal').modal('hide');
            $('#shareText').val('');
            loadPosts();
        } else {
            showError('Failed to share post');
        }
    } catch (error) {
        console.error('Error sharing post:', error);
        showError('Failed to share post');
    }
}

async function showComments(postId) {
    currentCommentsPostId = postId;
    $('#commentsModal').modal('show');

    try {
        const result = await newsFeedService.getComments(postId);
        if (result.success) {
            displayComments(result.data);
        }
    } catch (error) {
        console.error('Error loading comments:', error);
        showError('Failed to load comments');
    }
}

function displayComments(comments) {
    const container = $('#commentsContainer');

    if (comments.length === 0) {
        container.html('<p class="text-muted text-center">No comments yet. Be the first to comment!</p>');
        return;
    }

    let html = '';
    comments.forEach(comment => {
        const timeAgo = getTimeAgo(comment.createdAt);
        const userInitials = comment.authorName.split(' ').map(n => n[0]).join('').toUpperCase();

        html += `
            <div class="comment-item">
                <div class="d-flex">
                    <div class="user-avatar me-3" style="width: 32px; height: 32px; font-size: 0.8rem;">${userInitials}</div>
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <strong class="me-2">${comment.authorName}</strong>
                            <span class="user-type-badge user-type-${comment.authorType} me-2">${comment.authorType}</span>
                            <small class="text-muted">${timeAgo}</small>
                        </div>
                        <p class="mb-0">${comment.content}</p>
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);
}

async function addComment() {
    const commentText = $('#commentText').val().trim();

    if (!commentText) {
        showError('Please enter a comment');
        return;
    }

    try {
        const commentData = {
            authorId: currentUser.id,
            authorName: currentUser.username,
            authorType: currentUser.userType,
            content: commentText
        };

        const result = await newsFeedService.addComment(currentCommentsPostId, commentData);
        if (result.success) {
            $('#commentText').val('');
            showComments(currentCommentsPostId); // Refresh comments
            loadPosts(); // Refresh posts to update comment count
        } else {
            showError('Failed to add comment');
        }
    } catch (error) {
        console.error('Error adding comment:', error);
        showError('Failed to add comment');
    }
}

async function deletePost(postId) {
    if (!confirm('Are you sure you want to delete this post?')) {
        return;
    }

    try {
        const result = await newsFeedService.deletePost(postId, currentUser.id);
        if (result.success) {
            showSuccess('Post deleted successfully!');
            loadPosts();
        } else {
            showError('Failed to delete post');
        }
    } catch (error) {
        console.error('Error deleting post:', error);
        showError('Failed to delete post');
    }
}

function setupRealTimeListener() {
    // Set up real-time listener for new posts
    newsFeedService.listenToPosts((posts) => {
        displayPosts(posts);
    });
}

async function testFirebaseConnection() {
    console.log('Testing Firebase connection...');

    if (!newsFeedService) {
        showError('News feed service not initialized');
        return;
    }

    try {
        // Test creating a simple post
        const testPost = {
            authorId: currentUser.id,
            authorName: currentUser.username + ' (TEST)',
            authorType: currentUser.userType,
            content: 'This is a test post to verify Firebase connection - ' + new Date().toISOString(),
            type: 'original',
            mediaUrls: []
        };

        console.log('Creating test post:', testPost);
        const result = await newsFeedService.createPost(testPost);
        console.log('Test post result:', result);

        if (result.success) {
            showSuccess('Firebase connection successful! Test post created.');
            loadPosts(); // Refresh to show the test post
        } else {
            showError('Firebase connection failed: ' + result.error);
        }
    } catch (error) {
        console.error('Firebase test error:', error);
        showError('Firebase test failed: ' + error.message);
    }
}

function refreshFeed() {
    loadPosts();
    showSuccess('Feed refreshed!');
}

function loadMorePosts() {
    // Implement pagination if needed
    showInfo('Load more functionality can be implemented here');
}

function addPoll() {
    showInfo('Poll feature coming soon!');
}

function addEvent() {
    showInfo('Event feature coming soon!');
}

function getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + 'm ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + 'h ago';
    if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + 'd ago';

    return time.toLocaleDateString();
}

function showSuccess(message) {
    showToast(message, 'success');
}

function showError(message) {
    showToast(message, 'danger');
}

function showInfo(message) {
    showToast(message, 'info');
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 100px; right: 20px; z-index: 1050; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>
{% endblock %}
