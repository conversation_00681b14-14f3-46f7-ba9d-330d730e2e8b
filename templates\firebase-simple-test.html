<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Firebase Test - JobSync</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-console {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 1rem;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-fire me-2"></i>Simple Firebase Test
                    <span class="badge bg-info ms-2">CDN Version</span>
                </h1>
                
                <!-- Test Form -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Create Test Post</h5>
                            </div>
                            <div class="card-body">
                                <form id="testPostForm">
                                    <div class="mb-3">
                                        <label for="testContent" class="form-label">Post Content</label>
                                        <textarea class="form-control" id="testContent" rows="3" 
                                                  placeholder="Enter test post content...">Hello from JobSync! This is a test post to verify Firebase connectivity.</textarea>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary" id="createBtn">
                                            <i class="fas fa-plus me-1"></i>Create Post
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="loadTestPosts()">
                                            <i class="fas fa-eye me-1"></i>Load Posts
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="clearTestConsole()">
                                            <i class="fas fa-trash me-1"></i>Clear
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Firebase Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>CDN Status:</strong> 
                                    <span id="cdnStatus" class="badge bg-secondary">Loading...</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Firebase App:</strong> 
                                    <span id="appStatus" class="badge bg-secondary">Waiting...</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Firestore:</strong> 
                                    <span id="firestoreStatus" class="badge bg-secondary">Waiting...</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Service:</strong> 
                                    <span id="serviceStatus" class="badge bg-secondary">Waiting...</span>
                                </div>
                                <div class="mt-3">
                                    <a href="/newsfeed" class="btn btn-outline-primary">
                                        <i class="fas fa-arrow-left me-1"></i>Back to News Feed
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Console -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Test Console</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="test-console" id="test-console">
                                    <div>Simple Firebase Test Console - JobSync</div>
                                    <div>Loading Firebase CDN...</div>
                                    <div>---</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/firebase-basic-test.js') }}"></script>
    
    <script>
        let basicService = null;
        
        function testLog(message, type = 'info') {
            const console = document.getElementById('test-console');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : type === 'warning' ? '#ffd43b' : '#00ff00';
            console.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }
        
        function updateStatus(component, status, text) {
            const statusEl = document.getElementById(`${component}Status`);
            const className = status === 'success' ? 'bg-success' : status === 'error' ? 'bg-danger' : status === 'warning' ? 'bg-warning' : 'bg-info';
            statusEl.className = `badge ${className}`;
            statusEl.textContent = text;
        }
        
        function clearTestConsole() {
            document.getElementById('test-console').innerHTML = `
                <div>Simple Firebase Test Console - JobSync</div>
                <div>Console cleared</div>
                <div>---</div>
            `;
        }
        
        // Monitor Firebase initialization
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            checkCount++;
            
            if (typeof firebase !== 'undefined') {
                updateStatus('cdn', 'success', 'Loaded');
                testLog('✅ Firebase CDN loaded successfully', 'success');
            }
            
            if (window.firebaseBasicReady === true) {
                clearInterval(checkInterval);
                testLog('🎉 Firebase basic service is ready!', 'success');
                updateStatus('app', 'success', 'Initialized');
                updateStatus('firestore', 'success', 'Connected');
                updateStatus('service', 'success', 'Ready');
                basicService = window.basicFirebaseService;
            } else if (window.firebaseBasicReady === false) {
                clearInterval(checkInterval);
                testLog('❌ Firebase basic service failed to initialize', 'error');
                updateStatus('app', 'error', 'Failed');
                updateStatus('firestore', 'error', 'Failed');
                updateStatus('service', 'error', 'Failed');
            } else if (checkCount > 30) {
                clearInterval(checkInterval);
                testLog('⏰ Timeout waiting for Firebase initialization', 'warning');
                updateStatus('app', 'warning', 'Timeout');
            } else {
                testLog(`⏳ Waiting for Firebase... (${checkCount}/30)`, 'info');
            }
        }, 1000);
        
        // Form submission
        $('#testPostForm').submit(async function(e) {
            e.preventDefault();
            
            if (!basicService) {
                testLog('❌ Firebase service not available', 'error');
                return;
            }
            
            const content = $('#testContent').val().trim();
            if (!content) {
                testLog('❌ Please enter some content', 'error');
                return;
            }
            
            const createBtn = $('#createBtn');
            const originalText = createBtn.html();
            createBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Creating...').prop('disabled', true);
            
            try {
                testLog('📝 Creating test post...', 'info');
                
                const postData = {
                    authorId: 'test-user-{{ session.user_id }}',
                    authorName: 'Test User ({{ session.username }})',
                    authorType: '{{ session.user_type }}',
                    content: content,
                    type: 'original',
                    mediaUrls: []
                };
                
                const result = await basicService.createPost(postData);
                
                if (result.success) {
                    testLog(`✅ Post created successfully with ID: ${result.id}`, 'success');
                    $('#testContent').val('');
                    
                    // Auto-load posts to show the new one
                    setTimeout(loadTestPosts, 1000);
                } else {
                    testLog(`❌ Failed to create post: ${result.error}`, 'error');
                }
                
            } catch (error) {
                testLog(`❌ Error creating post: ${error.message}`, 'error');
            } finally {
                createBtn.html(originalText).prop('disabled', false);
            }
        });
        
        async function loadTestPosts() {
            if (!basicService) {
                testLog('❌ Firebase service not available', 'error');
                return;
            }
            
            try {
                testLog('📖 Loading posts...', 'info');
                const result = await basicService.getPosts(5);
                
                if (result.success) {
                    testLog(`✅ Loaded ${result.data.length} posts`, 'success');
                    result.data.forEach((post, index) => {
                        const preview = post.content.length > 50 ? post.content.substring(0, 50) + '...' : post.content;
                        testLog(`📄 ${index + 1}. "${preview}" by ${post.authorName}`, 'info');
                    });
                } else {
                    testLog(`❌ Failed to load posts: ${result.error}`, 'error');
                }
                
            } catch (error) {
                testLog(`❌ Error loading posts: ${error.message}`, 'error');
            }
        }
        
        // Initial log
        testLog('🚀 Test console initialized', 'info');
        testLog('⏳ Waiting for Firebase CDN...', 'info');
    </script>
</body>
</html>
