#!/usr/bin/env python3
"""
Test Application Actions - Add Sample Applications
=================================================
"""

import sqlite3
from datetime import datetime, timedelta
import random

def add_sample_applications():
    """Add sample applications for testing the Yes/No functionality"""
    
    conn = sqlite3.connect('jobportal.db')
    cursor = conn.cursor()
    
    try:
        # Get existing users and jobs
        users = cursor.execute('SELECT id, username, email FROM users WHERE user_type = "jobseeker"').fetchall()
        jobs = cursor.execute('SELECT id, title, employer_id FROM jobs').fetchall()
        
        if not users or not jobs:
            print("❌ No users or jobs found. Please create some first.")
            return
        
        print(f"Found {len(users)} jobseekers and {len(jobs)} jobs")
        
        # Clear existing applications for clean testing
        cursor.execute('DELETE FROM applications')
        print("🧹 Cleared existing applications")
        
        # Add sample applications
        applications_added = 0
        
        for i in range(min(10, len(users))):  # Add up to 10 applications
            user = users[i % len(users)]
            job = jobs[i % len(jobs)]
            
            # Random compatibility score
            compatibility_score = random.randint(40, 95)
            
            # Random application date (last 30 days)
            days_ago = random.randint(1, 30)
            applied_at = (datetime.now() - timedelta(days=days_ago)).strftime('%Y-%m-%d %H:%M:%S')
            
            try:
                cursor.execute('''
                    INSERT INTO applications (job_id, applicant_id, employer_id, compatibility_score, status, applied_at, updated_at)
                    VALUES (?, ?, ?, ?, 'pending', ?, ?)
                ''', (job[0], user[0], job[2], compatibility_score, applied_at, applied_at))
                
                applications_added += 1
                print(f"✅ Added application: {user[1]} -> {job[1]} ({compatibility_score}%)")
                
            except sqlite3.IntegrityError:
                print(f"⚠️ Skipped duplicate application: {user[1]} -> {job[1]}")
        
        conn.commit()
        print(f"\n🎉 Successfully added {applications_added} sample applications!")
        
        # Show current applications
        print("\n📋 Current Applications:")
        applications = cursor.execute('''
            SELECT a.id, u.username, j.title, a.compatibility_score, a.status, a.applied_at
            FROM applications a
            JOIN users u ON a.applicant_id = u.id
            JOIN jobs j ON a.job_id = j.id
            ORDER BY a.applied_at DESC
        ''').fetchall()
        
        for app in applications:
            print(f"  {app[0]}: {app[1]} -> {app[2]} ({app[3]}%) - {app[4]} - {app[5]}")
        
        print(f"\n🚀 Ready to test! Login as employer and check the dashboard.")
        print("📧 Demo employer: <EMAIL> / demo123")
        print("🌐 URL: http://127.0.0.1:8080")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        conn.close()

def test_application_status_update():
    """Test the application status update functionality"""
    
    conn = sqlite3.connect('jobportal.db')
    cursor = conn.cursor()
    
    try:
        # Get a pending application
        app = cursor.execute('''
            SELECT a.id, u.username, j.title, a.status
            FROM applications a
            JOIN users u ON a.applicant_id = u.id
            JOIN jobs j ON a.job_id = j.id
            WHERE a.status = 'pending'
            LIMIT 1
        ''').fetchone()
        
        if not app:
            print("❌ No pending applications found")
            return
        
        print(f"📋 Testing with application: {app[1]} -> {app[2]} (ID: {app[0]})")
        
        # Test accepting an application
        cursor.execute('''
            UPDATE applications 
            SET status = 'accepted', updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (app[0],))
        
        conn.commit()
        print(f"✅ Application {app[0]} marked as ACCEPTED")
        
        # Verify the update
        updated_app = cursor.execute('''
            SELECT status, updated_at FROM applications WHERE id = ?
        ''', (app[0],)).fetchone()
        
        print(f"📊 Status: {updated_app[0]}, Updated: {updated_app[1]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    print("🧪 JobSync Application Actions Test")
    print("=" * 40)
    
    choice = input("\n1. Add sample applications\n2. Test status update\n3. Both\nChoice (1-3): ").strip()
    
    if choice in ['1', '3']:
        add_sample_applications()
    
    if choice in ['2', '3']:
        print("\n" + "=" * 40)
        test_application_status_update()
    
    print("\n✅ Test completed!")
