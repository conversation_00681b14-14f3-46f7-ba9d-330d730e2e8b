{% extends "base.html" %}

{% block title %}Profile - JobSync{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-user-edit me-2"></i>Profile Settings
        </h1>
    </div>
</div>

<div class="row">
    <!-- User Information -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>User Information
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-muted"></i>
                </div>
                <h5>{{ user.username }}</h5>
                <p class="text-muted">{{ user.email }}</p>
                <span class="user-type-badge user-type-{{ user.user_type }}">
                    {{ user.user_type.title() }}
                </span>
                {% if user.company_name %}
                <p class="mt-2">
                    <i class="fas fa-building me-1"></i>
                    {{ user.company_name }}
                </p>
                {% endif %}
                <small class="text-muted">
                    Member since: {{ user.created_at.split(' ')[0] if user.created_at else 'Recently' }}
                </small>
            </div>
        </div>
    </div>

    <!-- Profile Form -->
    <div class="col-lg-8">
        {% if session.user_type == 'jobseeker' %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Update Your Profile
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="skills" class="form-label">Skills</label>
                        <textarea class="form-control" id="skills" name="skills" rows="3"
                                  placeholder="Enter your skills separated by commas (e.g., Python, JavaScript, React)">{{ profile.skills if profile else '' }}</textarea>
                        <div class="form-text">List your technical and professional skills separated by commas.</div>
                    </div>

                    <div class="mb-3">
                        <label for="experience" class="form-label">Experience</label>
                        <textarea class="form-control" id="experience" name="experience" rows="4"
                                  placeholder="Describe your work experience">{{ profile.experience if profile else '' }}</textarea>
                        <div class="form-text">Describe your relevant work experience and achievements.</div>
                    </div>

                    <div class="mb-3">
                        <label for="education" class="form-label">Education</label>
                        <textarea class="form-control" id="education" name="education" rows="3"
                                  placeholder="Describe your educational background">{{ profile.education if profile else '' }}</textarea>
                        <div class="form-text">Include your degrees, certifications, and relevant coursework.</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Profile
                    </button>
                    <a href="{{ url_for('jobseeker_dashboard') }}" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </form>
            </div>
        </div>
        {% else %}
        <!-- Employer Profile -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>Company Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Company Name:</label>
                            <p class="form-control-plaintext">{{ user.company_name or 'Not specified' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Contact Email:</label>
                            <p class="form-control-plaintext">{{ user.email }}</p>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    To update your company information, please contact support.
                </div>

                <a href="{{ url_for('employer_dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% if session.user_type == 'jobseeker' and profile %}
<!-- Skills Preview -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>Skills Preview
                </h5>
            </div>
            <div class="card-body">
                {% if profile.skills %}
                <div class="d-flex flex-wrap gap-2">
                    {% for skill in profile.skills.split(',') %}
                        <span class="skill-tag">{{ skill.strip() }}</span>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">No skills added yet. Update your profile to add skills.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Resume Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>Resume Information
                </h5>
            </div>
            <div class="card-body">
                {% if profile.resume_filename %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Resume uploaded:</strong> {{ profile.resume_filename }}
                    <br>
                    <small class="text-muted">
                        Your resume has been processed and skills have been extracted automatically.
                    </small>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>No resume uploaded yet.</strong>
                    <br>
                    <small>
                        Upload your resume from the dashboard to automatically extract skills and improve job matching.
                    </small>
                </div>
                {% endif %}

                <a href="{{ url_for('jobseeker_dashboard') }}" class="btn btn-outline-primary">
                    <i class="fas fa-upload me-2"></i>Manage Resume
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Character counters for textareas
    $('textarea').each(function() {
        const maxLength = 1000;
        const $this = $(this);
        const $counter = $('<div class="form-text text-end mt-1"></div>');

        $this.after($counter);

        function updateCounter() {
            const length = $this.val().length;
            $counter.text(`${length}/${maxLength} characters`);

            if (length > maxLength * 0.9) {
                $counter.addClass('text-warning');
            } else {
                $counter.removeClass('text-warning');
            }
        }

        $this.on('input', updateCounter);
        updateCounter();
    });

    // Skills input helper
    $('#skills').on('input', function() {
        const skills = $(this).val().split(',');
        const skillCount = skills.filter(skill => skill.trim().length > 0).length;

        if (skillCount > 20) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Too many skills. Please limit to 20 skills maximum.</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Form validation
    $('form').submit(function(e) {
        let isValid = true;

        // Check skills count
        const skills = $('#skills').val().split(',').filter(skill => skill.trim().length > 0);
        if (skills.length > 20) {
            isValid = false;
            alert('Please limit your skills to 20 maximum.');
        }

        // Check textarea lengths
        $('textarea').each(function() {
            if ($(this).val().length > 1000) {
                isValid = false;
                alert('Please keep each field under 1000 characters.');
                return false;
            }
        });

        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
