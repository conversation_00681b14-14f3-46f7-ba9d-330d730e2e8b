#!/usr/bin/env python3
"""
Fix Database Schema for Applications Table
==========================================
"""

import sqlite3
from datetime import datetime

def fix_applications_table():
    """Fix the applications table to add updated_at column"""
    
    conn = sqlite3.connect('jobportal.db')
    cursor = conn.cursor()
    
    try:
        # Check current schema
        cursor.execute('PRAGMA table_info(applications)')
        columns = [column[1] for column in cursor.fetchall()]
        print(f"Current columns: {columns}")
        
        if 'updated_at' not in columns:
            print("Adding updated_at column...")
            
            # Add the column without default value first
            cursor.execute('ALTER TABLE applications ADD COLUMN updated_at TIMESTAMP')
            
            # Update existing records with current timestamp
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute('UPDATE applications SET updated_at = ? WHERE updated_at IS NULL', (current_time,))
            
            conn.commit()
            print("✅ Successfully added updated_at column")
        else:
            print("ℹ️ updated_at column already exists")
        
        # Verify the fix
        cursor.execute('PRAGMA table_info(applications)')
        columns = [column[1] for column in cursor.fetchall()]
        print(f"Updated columns: {columns}")
        
        # Show sample data
        cursor.execute('SELECT id, status, applied_at, updated_at FROM applications LIMIT 3')
        sample_data = cursor.fetchall()
        print(f"Sample data: {sample_data}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_applications_table()
