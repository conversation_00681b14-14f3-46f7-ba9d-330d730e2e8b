{% extends "base.html" %}

{% block title %}Register - JobSync{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header text-center">
                <h3 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>Create Account
                </h3>
                <p class="text-muted mb-0">Join <PERSON>Sync today and sync your career!</p>
            </div>
            <div class="card-body">
                <form method="POST" id="registerForm">
                    <!-- User Type Selection -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">I am a:</label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="user_type" id="jobseeker" value="jobseeker" checked>
                                    <label class="form-check-label" for="jobseeker">
                                        <i class="fas fa-user me-1"></i>Job Seeker
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="user_type" id="employer" value="employer">
                                    <label class="form-check-label" for="employer">
                                        <i class="fas fa-building me-1"></i>Employer
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Username -->
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                    </div>

                    <!-- Company Name (for employers) -->
                    <div class="mb-3" id="companyNameDiv" style="display: none;">
                        <label for="company_name" class="form-label">Company Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-building"></i>
                            </span>
                            <input type="text" class="form-control" id="company_name" name="company_name">
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>

                    <!-- Password -->
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">Password must be at least 6 characters long.</div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="passwordMatch" class="form-text"></div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </button>
                    </div>
                </form>

                <div class="text-center mt-3">
                    <p class="text-muted">
                        Already have an account?
                        <a href="{{ url_for('login') }}" class="text-decoration-none">
                            Sign in here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Toggle user type specific fields
    $('input[name="user_type"]').change(function() {
        if ($(this).val() === 'employer') {
            $('#companyNameDiv').show();
            $('#company_name').attr('required', true);
        } else {
            $('#companyNameDiv').hide();
            $('#company_name').attr('required', false);
        }
    });

    // Toggle password visibility
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    $('#toggleConfirmPassword').click(function() {
        const passwordField = $('#confirm_password');
        const icon = $(this).find('i');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Password matching validation
    $('#confirm_password').on('input', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        const matchDiv = $('#passwordMatch');

        if (confirmPassword === '') {
            matchDiv.text('');
            return;
        }

        if (password === confirmPassword) {
            matchDiv.text('✓ Passwords match').removeClass('text-danger').addClass('text-success');
        } else {
            matchDiv.text('✗ Passwords do not match').removeClass('text-success').addClass('text-danger');
        }
    });

    // Form validation
    $('#registerForm').submit(function(e) {
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();

        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long.');
            return false;
        }

        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match.');
            return false;
        }

        if ($('input[name="user_type"]:checked').val() === 'employer' && $('#company_name').val().trim() === '') {
            e.preventDefault();
            alert('Company name is required for employers.');
            return false;
        }
    });
});
</script>
{% endblock %}
