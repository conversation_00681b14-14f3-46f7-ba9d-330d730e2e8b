#!/usr/bin/env python3
"""
Email Setup Script for JobSync
==============================

This script helps you configure email settings for sending real emails.
Run this script to set up your email credentials interactively.
"""

import os
import getpass

def setup_email_config():
    """Interactive email configuration setup"""
    print("🔧 JobSync Email Configuration Setup")
    print("=" * 50)
    print()
    
    print("This script will help you configure email settings for JobSync.")
    print("You can choose to use real email sending or demo mode.")
    print()
    
    # Ask if user wants real email
    while True:
        use_real = input("Do you want to send real emails? (y/n): ").lower().strip()
        if use_real in ['y', 'yes', 'n', 'no']:
            use_real_email = use_real in ['y', 'yes']
            break
        print("Please enter 'y' for yes or 'n' for no.")
    
    if not use_real_email:
        print("\n✅ Demo mode selected. Emails will be logged but not sent.")
        update_config(use_real_email=False)
        return
    
    print("\n📧 Real email mode selected. Let's configure your email settings.")
    print()
    
    # Email provider selection
    print("Select your email provider:")
    print("1. Gmail")
    print("2. Outlook/Hotmail")
    print("3. Yahoo Mail")
    print("4. Custom SMTP")
    
    while True:
        try:
            choice = int(input("\nEnter your choice (1-4): "))
            if 1 <= choice <= 4:
                break
            print("Please enter a number between 1 and 4.")
        except ValueError:
            print("Please enter a valid number.")
    
    # Configure based on provider
    if choice == 1:  # Gmail
        server = 'smtp.gmail.com'
        port = 587
        print("\n📧 Gmail selected.")
        print("You'll need to:")
        print("1. Enable 2-Factor Authentication on your Gmail account")
        print("2. Generate an App Password (not your regular password)")
        print("3. Visit: https://myaccount.google.com/apppasswords")
        
    elif choice == 2:  # Outlook
        server = 'smtp-mail.outlook.com'
        port = 587
        print("\n📧 Outlook/Hotmail selected.")
        
    elif choice == 3:  # Yahoo
        server = 'smtp.mail.yahoo.com'
        port = 587
        print("\n📧 Yahoo Mail selected.")
        
    else:  # Custom
        server = input("\nEnter SMTP server: ").strip()
        while True:
            try:
                port = int(input("Enter SMTP port (usually 587): "))
                break
            except ValueError:
                print("Please enter a valid port number.")
    
    # Get email credentials
    print()
    email = input("Enter your email address: ").strip()
    
    if choice == 1:  # Gmail
        print("\nFor Gmail, use your App Password (16 characters), not your regular password.")
    
    password = getpass.getpass("Enter your email password/app password: ")
    
    # Test email configuration
    print("\n🧪 Testing email configuration...")
    
    if test_email_config(server, port, email, password):
        print("✅ Email configuration test successful!")
        update_config(
            use_real_email=True,
            server=server,
            port=port,
            email=email,
            password=password
        )
        print("\n🎉 Email configuration saved successfully!")
        print("You can now use the forgot password feature with real emails.")
    else:
        print("❌ Email configuration test failed.")
        print("Please check your credentials and try again.")

def test_email_config(server, port, email, password):
    """Test email configuration"""
    try:
        import smtplib
        
        server_obj = smtplib.SMTP(server, port)
        server_obj.starttls()
        server_obj.login(email, password)
        server_obj.quit()
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

def update_config(use_real_email=False, server='smtp.gmail.com', port=587, email='', password=''):
    """Update email configuration file"""
    
    config_content = f'''#!/usr/bin/env python3
"""
Email Configuration for JobSync
===============================

This file contains email configuration settings for sending real emails.
Auto-generated by setup_email.py script.
"""

# Email Configuration
EMAIL_CONFIG = {{
    # Set to True to send real emails, False for demo mode
    'USE_REAL_EMAIL': {use_real_email},
    
    # SMTP Configuration
    'MAIL_SERVER': '{server}',
    'MAIL_PORT': {port},
    'MAIL_USE_TLS': True,
    
    # Email credentials
    'MAIL_USERNAME': '{email}',
    'MAIL_PASSWORD': '{password}',
}}

def get_email_config():
    """Get email configuration"""
    return EMAIL_CONFIG
'''
    
    with open('email_config.py', 'w') as f:
        f.write(config_content)

def main():
    """Main setup function"""
    try:
        setup_email_config()
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")

if __name__ == "__main__":
    main()
