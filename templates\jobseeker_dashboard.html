{% extends "base.html" %}

{% block title %}Dashboard - JobSync{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Welcome back, {{ session.username }}!
            <span class="user-type-badge user-type-jobseeker ms-2">Job Seeker</span>
        </h1>
    </div>
</div>

<div class="row">
    <!-- Resume Upload Section -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>Resume Management
                </h5>
            </div>
            <div class="card-body">
                {% if profile and profile.resume_filename %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Resume uploaded successfully!</strong>
                        <br>
                        <small class="text-muted">File: {{ profile.resume_filename }}</small>
                    </div>

                    {% if profile.skills %}
                    <div class="mb-3">
                        <h6>Extracted Skills:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            {% for skill in profile.skills.split(',') %}
                                <span class="skill-tag">{{ skill.strip() }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    {% if profile.experience %}
                    <div class="mb-3">
                        <h6>Experience:</h6>
                        <p class="text-muted">{{ profile.experience }}</p>
                    </div>
                    {% endif %}

                    {% if profile.education %}
                    <div class="mb-3">
                        <h6>Education:</h6>
                        <p class="text-muted">{{ profile.education }}</p>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-upload fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Upload your resume to get started with automatic skill extraction</p>
                    </div>
                {% endif %}

                <form action="{{ url_for('upload_resume') }}" method="POST" enctype="multipart/form-data" class="mt-3">
                    <div class="mb-3">
                        <label for="resume" class="form-label">Upload Resume (PDF only)</label>
                        <input type="file" class="form-control" id="resume" name="resume" accept=".pdf" required>
                        <div class="form-text">Maximum file size: 16MB</div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        {% if profile and profile.resume_filename %}Update Resume{% else %}Upload Resume{% endif %}
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Profile Summary -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Profile Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="fw-bold text-muted">Email:</label>
                    <p class="mb-2">{{ session.get('email', 'Not available') }}</p>
                </div>

                <div class="mb-3">
                    <label class="fw-bold text-muted">Skills:</label>
                    {% if profile and profile.skills %}
                        <div class="d-flex flex-wrap gap-1 mt-1">
                            {% for skill in profile.skills.split(',') %}
                                <span class="skill-tag">{{ skill.strip() }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">Upload your resume to extract skills</p>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label class="fw-bold text-muted">Experience:</label>
                    <p class="text-muted">{{ profile.experience if profile and profile.experience else 'Not specified' }}</p>
                </div>

                <div class="mb-3">
                    <label class="fw-bold text-muted">Education:</label>
                    <p class="text-muted">{{ profile.education if profile and profile.education else 'Not specified' }}</p>
                </div>

                <a href="{{ url_for('profile') }}" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>Edit Profile
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('jobs') }}" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <br>Browse Jobs
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('newsfeed') }}" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-newspaper fa-2x mb-2"></i>
                            <br>News Feed
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('chat') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <br>SuperChat
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('profile') }}" class="btn btn-outline-warning btn-lg w-100">
                            <i class="fas fa-user-edit fa-2x mb-2"></i>
                            <br>Update Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Applications -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>Recent Applications
                </h5>
                <a href="{{ url_for('jobs') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>Apply for More Jobs
                </a>
            </div>
            <div class="card-body">
                {% if applications %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Job Title</th>
                                    <th>Company</th>
                                    <th>Location</th>
                                    <th>Compatibility</th>
                                    <th>Status</th>
                                    <th>Applied Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for app in applications %}
                                <tr>
                                    <td>
                                        <strong>{{ app.job_title }}</strong>
                                    </td>
                                    <td>{{ app.company_name or 'N/A' }}</td>
                                    <td>
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ app.location }}
                                    </td>
                                    <td>
                                        <span class="compatibility-score">
                                            <i class="fas fa-star me-1"></i>
                                            {{ app.compatibility_score }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% if app.status == 'pending' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock me-1"></i>Pending
                                            </span>
                                        {% elif app.status == 'accepted' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Accepted
                                            </span>
                                        {% else %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times me-1"></i>Rejected
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ app.applied_at.split(' ')[0] if app.applied_at else 'N/A' }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No applications yet</h5>
                        <p class="text-muted">Start browsing jobs and apply to positions that match your skills!</p>
                        <a href="{{ url_for('jobs') }}" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Browse Jobs
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh applications every 30 seconds
    setInterval(function() {
        // You can implement AJAX refresh here if needed
    }, 30000);

    // File upload validation
    $('#resume').change(function() {
        const file = this.files[0];
        if (file) {
            if (file.size > 16 * 1024 * 1024) {
                alert('File size must be less than 16MB');
                $(this).val('');
                return;
            }

            if (!file.name.toLowerCase().endsWith('.pdf')) {
                alert('Please select a PDF file');
                $(this).val('');
                return;
            }
        }
    });
});
</script>
{% endblock %}
