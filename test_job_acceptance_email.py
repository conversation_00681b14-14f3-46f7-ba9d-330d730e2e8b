#!/usr/bin/env python3
"""
Test Job Acceptance Email Functionality
=======================================
"""

import sqlite3
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_job_acceptance_email():
    """Test the job acceptance email functionality"""
    
    print("🧪 Testing Job Acceptance Email Functionality")
    print("=" * 50)
    
    # Import the email function
    try:
        from app import send_job_offer_email
        print("✅ Successfully imported send_job_offer_email function")
    except ImportError as e:
        print(f"❌ Failed to import function: {e}")
        return
    
    # Get a sample application from database
    conn = sqlite3.connect('jobportal.db')
    conn.row_factory = sqlite3.Row
    
    try:
        # Get a pending application with all required details
        application = conn.execute('''
            SELECT a.*, j.title as job_title, j.salary, j.location, j.job_type,
                   u.username as candidate_name, u.email as candidate_email,
                   emp.username as employer_name, emp.company_name
            FROM applications a
            JOIN jobs j ON a.job_id = j.id
            JOIN users u ON a.applicant_id = u.id
            JOIN users emp ON a.employer_id = emp.id
            WHERE a.status = 'pending'
            LIMIT 1
        ''').fetchone()
        
        if not application:
            print("❌ No pending applications found for testing")
            print("💡 Run test_application_actions.py first to create sample applications")
            return
        
        print(f"\n📋 Testing with application:")
        print(f"   Candidate: {application['candidate_name']} ({application['candidate_email']})")
        print(f"   Job: {application['job_title']}")
        print(f"   Company: {application['company_name'] or application['employer_name']}")
        print(f"   Salary: {application['salary'] or 'Not specified'}")
        print(f"   Location: {application['location']}")
        
        # Test the email function
        print(f"\n📧 Sending job acceptance email...")
        
        success = send_job_offer_email(application)
        
        if success:
            print("✅ Job acceptance email sent successfully!")
            print(f"📬 Email sent to: {application['candidate_email']}")
            print(f"📄 Subject: 🎉 CONGRATULATIONS! You've Been Selected - {application['job_title']} at {application['company_name'] or application['employer_name']}")
        else:
            print("❌ Failed to send job acceptance email")
        
        # Update application status to accepted for testing
        conn.execute('''
            UPDATE applications 
            SET status = 'accepted', updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (application['id'],))
        conn.commit()
        
        print(f"✅ Application {application['id']} marked as ACCEPTED in database")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
    finally:
        conn.close()

def test_email_content():
    """Test the email content generation"""
    
    print("\n🔍 Testing Email Content Generation")
    print("=" * 40)
    
    # Create a mock application for testing
    mock_application = {
        'candidate_email': '<EMAIL>',
        'candidate_name': 'John Doe',
        'job_title': 'Senior Software Engineer',
        'company_name': 'TechCorp Inc.',
        'employer_name': 'TechCorp Inc.',
        'salary': '75,000 CFA per month',
        'location': 'Douala, Cameroon',
        'job_type': 'full-time'
    }
    
    print("📝 Mock Application Data:")
    for key, value in mock_application.items():
        print(f"   {key}: {value}")
    
    try:
        from app import send_job_offer_email
        
        print(f"\n📧 Generating email content...")
        success = send_job_offer_email(mock_application)
        
        if success:
            print("✅ Email content generated and sent successfully!")
        else:
            print("❌ Failed to generate/send email content")
            
    except Exception as e:
        print(f"❌ Error generating email content: {e}")

def show_email_preview():
    """Show a preview of what the email looks like"""
    
    print("\n👀 Email Content Preview")
    print("=" * 30)
    
    # Sample data
    candidate_name = "Alice Johnson"
    job_title = "Data Scientist"
    company_name = "DataTech Solutions"
    salary = "80,000 CFA per month"
    location = "Yaoundé, Cameroon"
    job_type = "full-time"
    
    # Generate preview text
    preview_text = f"""
📧 EMAIL PREVIEW:
================

TO: <EMAIL>
SUBJECT: 🎉 CONGRATULATIONS! You've Been Selected - {job_title} at {company_name}

CONTENT PREVIEW:
---------------

🎊 FANTASTIC NEWS! We are thrilled to inform you that your application has been ACCEPTED and we are officially offering you the position of {job_title} at {company_name}!

🎯 YOUR APPLICATION STATUS: ACCEPTED ✅

📋 JOB OFFER DETAILS:
- Position: {job_title}
- Company: {company_name}
- Salary: {salary}
- Location: {location}
- Employment Type: {job_type.replace('-', ' ').title()}
- Start Date: To be discussed

🚀 WHAT HAPPENS NEXT:
1. 📧 Confirmation Required - Please reply to this email to confirm your acceptance
2. 📋 Formal Documentation - We'll send you the official offer letter and contract
3. 📅 Start Date Discussion - We'll coordinate your preferred start date
4. 🎯 Onboarding Process - We'll guide you through orientation and paperwork
5. 🤝 Welcome Meeting - Schedule a meeting with your new team and manager

⏰ ACTION REQUIRED: Please respond within 7 days to confirm your acceptance of this position.

Welcome to the team!

Best regards,
{company_name} Hiring Team
"""
    
    print(preview_text)

def main():
    """Main test function"""
    
    choice = input("""
🧪 Job Acceptance Email Test Options:
=====================================

1. Test with real application data
2. Test with mock data
3. Show email preview
4. All tests

Choose option (1-4): """).strip()
    
    if choice in ['1', '4']:
        test_job_acceptance_email()
    
    if choice in ['2', '4']:
        test_email_content()
    
    if choice in ['3', '4']:
        show_email_preview()
    
    print(f"\n🎉 Testing completed!")
    print(f"\n📋 Summary:")
    print(f"✅ Job acceptance emails are sent when applications are accepted")
    print(f"✅ Emails include comprehensive job offer details")
    print(f"✅ Both HTML and plain text versions are supported")
    print(f"✅ Professional formatting with clear next steps")
    print(f"✅ Action required notice for candidate response")

if __name__ == "__main__":
    main()
