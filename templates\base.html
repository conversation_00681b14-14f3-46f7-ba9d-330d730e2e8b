<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}JobSync{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
        }

        body {
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }

        .card {
            border: none;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border-radius: 0.5rem;
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
        }

        .compatibility-score {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-weight: bold;
        }

        .skill-tag {
            background-color: #e0e7ff;
            color: #3730a3;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            margin: 0.125rem;
            display: inline-block;
        }

        .skill-tag.matched {
            background-color: #dcfce7;
            color: #166534;
        }

        .chat-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            background-color: #fff;
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
        }

        .message.own {
            background-color: var(--primary-color);
            color: white;
            margin-left: 20%;
        }

        .message.other {
            background-color: #f1f5f9;
            margin-right: 20%;
        }

        .user-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }

        .user-type-jobseeker {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .user-type-employer {
            background-color: #dcfce7;
            color: #166534;
        }

        .footer {
            background-color: #1e293b;
            color: #cbd5e1;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        /* Chatbot Styles */
        .chatbot-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .chatbot-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .chatbot-modal {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 400px;
            height: 600px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            display: none;
            overflow: hidden;
        }

        .chatbot-header {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            padding: 15px;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .chatbot-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            margin-left: auto;
        }

        @media (max-width: 768px) {
            .chatbot-modal {
                width: calc(100vw - 40px);
                height: 500px;
                bottom: 90px;
                right: 20px;
                left: 20px;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <i class="fas fa-sync-alt me-2"></i>JobSync
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if session.user_id %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('jobs') }}">
                                <i class="fas fa-search me-1"></i>Jobs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('newsfeed') }}">
                                <i class="fas fa-newspaper me-1"></i>News Feed
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('email_log') }}">
                                <i class="fas fa-envelope me-1"></i>Email Log
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('chat') }}">
                                <i class="fas fa-comments me-1"></i>SuperChat
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('evaluation_metrics') }}">
                                <i class="fas fa-chart-line me-1"></i>Analytics
                            </a>
                        </li>
                    {% endif %}
                </ul>

                <ul class="navbar-nav">
                    {% if session.user_id %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ session.username }}
                                <span class="user-type-badge user-type-{{ session.user_type }}">{{ session.user_type }}</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                    <i class="fas fa-user-edit me-2"></i>Profile
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="{{ url_for('register') }}">Register</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-sync-alt me-2"></i>JobSync</h5>
                    <p>Connecting jobseekers with employers through intelligent matching.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2024 JobSync. All rights reserved.</p>
                    <p>Built with Flask, Bootstrap & JavaScript</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Chatbot Integration -->
    <button class="chatbot-toggle" id="chatbotToggle" title="AI Assistant">
        <i class="fas fa-robot"></i>
    </button>

    <div class="chatbot-modal" id="chatbotModal">
        <div class="chatbot-header">
            <div>
                <h6 class="mb-0">
                    <i class="fas fa-robot me-2"></i>JobSync AI Assistant
                </h6>
                <small>Ask me anything about jobs!</small>
            </div>
            <button class="chatbot-close" id="chatbotClose">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <iframe
            src="https://www.chatbase.co/chatbot-iframe/WriYULIVkeK2kJ8z2K2Le"
            width="100%"
            style="height: calc(100% - 70px); border: none;">
        </iframe>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Chatbot JavaScript -->
    <script>
    $(document).ready(function() {
        // Chatbot toggle functionality
        $('#chatbotToggle').click(function() {
            $('#chatbotModal').fadeToggle(300);
            $(this).toggleClass('active');
        });

        $('#chatbotClose').click(function() {
            $('#chatbotModal').fadeOut(300);
            $('#chatbotToggle').removeClass('active');
        });

        // Close chatbot when clicking outside
        $(document).click(function(e) {
            if (!$(e.target).closest('#chatbotModal, #chatbotToggle').length) {
                $('#chatbotModal').fadeOut(300);
                $('#chatbotToggle').removeClass('active');
            }
        });

        // Prevent chatbot modal from closing when clicking inside
        $('#chatbotModal').click(function(e) {
            e.stopPropagation();
        });

        // Add pulse animation to chatbot button
        setInterval(function() {
            if (!$('#chatbotModal').is(':visible')) {
                $('#chatbotToggle').addClass('pulse');
                setTimeout(function() {
                    $('#chatbotToggle').removeClass('pulse');
                }, 1000);
            }
        }, 10000); // Pulse every 10 seconds
    });
    </script>

    <style>
    .chatbot-toggle.pulse {
        animation: pulse 1s ease-in-out;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .chatbot-toggle.active {
        background: linear-gradient(135deg, #10b981, #059669);
    }
    </style>

    {% block extra_js %}{% endblock %}
</body>
</html>
